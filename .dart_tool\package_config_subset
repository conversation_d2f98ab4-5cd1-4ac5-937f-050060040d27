doctor_appointment_app
3.0
file:///D:/Doctor%20Appointment%20App/
file:///D:/Doctor%20Appointment%20App/lib/
archive
3.0
file:///D:/FlutterCache/PubCache/hosted/pub.dev/archive-4.0.7/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/archive-4.0.7/lib/
args
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/args-2.7.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/async-2.13.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/async-2.13.0/lib/
boolean_selector
3.1
file:///D:/FlutterCache/PubCache/hosted/pub.dev/boolean_selector-2.1.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/boolean_selector-2.1.2/lib/
characters
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/characters-1.4.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
3.8
file:///D:/FlutterCache/PubCache/hosted/pub.dev/checked_yaml-2.0.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/checked_yaml-2.0.4/lib/
cli_util
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/cli_util-0.4.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/cli_util-0.4.2/lib/
clock
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/clock-1.1.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/collection-1.19.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/collection-1.19.1/lib/
connectivity_plus
2.18
file:///D:/FlutterCache/PubCache/hosted/pub.dev/connectivity_plus-5.0.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/connectivity_plus-5.0.2/lib/
connectivity_plus_platform_interface
2.12
file:///D:/FlutterCache/PubCache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/lib/
crypto
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/crypto-3.0.6/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///D:/FlutterCache/PubCache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dbus
2.17
file:///D:/FlutterCache/PubCache/hosted/pub.dev/dbus-0.7.11/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/dbus-0.7.11/lib/
fake_async
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/fake_async-1.3.3/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/fake_async-1.3.3/lib/
ffi
3.7
file:///D:/FlutterCache/PubCache/hosted/pub.dev/ffi-2.1.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///D:/FlutterCache/PubCache/hosted/pub.dev/file-7.0.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/file-7.0.1/lib/
flutter_bluetooth_serial
2.12
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_bluetooth_serial-0.4.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_bluetooth_serial-0.4.0/lib/
flutter_launcher_icons
2.18
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_launcher_icons-0.13.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_launcher_icons-0.13.1/lib/
flutter_lints
3.1
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_lints-3.0.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_lints-3.0.2/lib/
flutter_local_notifications
2.17
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_local_notifications-16.3.3/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_local_notifications-16.3.3/lib/
flutter_local_notifications_linux
2.17
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/
flutter_local_notifications_platform_interface
2.17
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/lib/
flutter_sms
2.12
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_sms-2.3.3/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/flutter_sms-2.3.3/lib/
go_router
3.0
file:///D:/FlutterCache/PubCache/hosted/pub.dev/go_router-12.1.3/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/go_router-12.1.3/lib/
http
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/http-1.4.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/http-1.4.0/lib/
http_parser
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/http_parser-4.1.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/http_parser-4.1.2/lib/
image
3.0
file:///D:/FlutterCache/PubCache/hosted/pub.dev/image-4.5.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/image-4.5.4/lib/
intl
2.12
file:///D:/FlutterCache/PubCache/hosted/pub.dev/intl-0.18.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/intl-0.18.1/lib/
js
2.19
file:///D:/FlutterCache/PubCache/hosted/pub.dev/js-0.6.7/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///D:/FlutterCache/PubCache/hosted/pub.dev/json_annotation-4.9.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/json_annotation-4.9.0/lib/
leak_tracker
3.2
file:///D:/FlutterCache/PubCache/hosted/pub.dev/leak_tracker-10.0.9/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///D:/FlutterCache/PubCache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///D:/FlutterCache/PubCache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.0
file:///D:/FlutterCache/PubCache/hosted/pub.dev/lints-3.0.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/lints-3.0.0/lib/
logging
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/logging-1.3.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/logging-1.3.0/lib/
matcher
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/matcher-0.12.17/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///D:/FlutterCache/PubCache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///D:/FlutterCache/PubCache/hosted/pub.dev/meta-1.16.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/meta-1.16.0/lib/
nested
2.12
file:///D:/FlutterCache/PubCache/hosted/pub.dev/nested-1.0.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///D:/FlutterCache/PubCache/hosted/pub.dev/nm-0.5.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/nm-0.5.0/lib/
path
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/path-1.9.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/path-1.9.1/lib/
path_provider_linux
2.19
file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
permission_handler
3.5
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler-11.4.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler-11.4.0/lib/
permission_handler_android
3.5
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_android-12.1.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_android-12.1.0/lib/
permission_handler_apple
2.18
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.5
file:///D:/FlutterCache/PubCache/hosted/pub.dev/petitparser-6.1.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/petitparser-6.1.0/lib/
platform
3.2
file:///D:/FlutterCache/PubCache/hosted/pub.dev/platform-3.1.6/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///D:/FlutterCache/PubCache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
posix
3.0
file:///D:/FlutterCache/PubCache/hosted/pub.dev/posix-6.0.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/posix-6.0.2/lib/
provider
2.12
file:///D:/FlutterCache/PubCache/hosted/pub.dev/provider-6.1.5/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/provider-6.1.5/lib/
shared_preferences
3.5
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences-2.5.3/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
source_span
3.1
file:///D:/FlutterCache/PubCache/hosted/pub.dev/source_span-1.10.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/source_span-1.10.1/lib/
sqflite
3.7
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite-2.4.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite-2.4.2/lib/
sqflite_android
3.7
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_android-2.4.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_android-2.4.1/lib/
sqflite_common
3.7
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_common-2.5.5/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_common-2.5.5/lib/
sqflite_darwin
3.7
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_darwin-2.4.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/
sqflite_platform_interface
3.5
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/stack_trace-1.12.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/stream_channel-2.1.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/stream_channel-2.1.4/lib/
string_scanner
3.1
file:///D:/FlutterCache/PubCache/hosted/pub.dev/string_scanner-1.4.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/string_scanner-1.4.1/lib/
synchronized
3.7
file:///D:/FlutterCache/PubCache/hosted/pub.dev/synchronized-3.3.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/synchronized-3.3.1/lib/
term_glyph
3.1
file:///D:/FlutterCache/PubCache/hosted/pub.dev/term_glyph-1.2.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///D:/FlutterCache/PubCache/hosted/pub.dev/test_api-0.7.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/test_api-0.7.4/lib/
timezone
2.19
file:///D:/FlutterCache/PubCache/hosted/pub.dev/timezone-0.9.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/timezone-0.9.4/lib/
typed_data
3.5
file:///D:/FlutterCache/PubCache/hosted/pub.dev/typed_data-1.4.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher-6.3.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher-6.3.1/lib/
url_launcher_android
3.6
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_ios
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_ios-6.3.3/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/
url_launcher_linux
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
vector_math
2.14
file:///D:/FlutterCache/PubCache/hosted/pub.dev/vector_math-2.1.4/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/vm_service-15.0.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/vm_service-15.0.0/lib/
web
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/web-1.1.1/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/web-1.1.1/lib/
xdg_directories
3.3
file:///D:/FlutterCache/PubCache/hosted/pub.dev/xdg_directories-1.1.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///D:/FlutterCache/PubCache/hosted/pub.dev/xml-6.5.0/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///D:/FlutterCache/PubCache/hosted/pub.dev/yaml-3.1.3/
file:///D:/FlutterCache/PubCache/hosted/pub.dev/yaml-3.1.3/lib/
sky_engine
3.7
file:///D:/flutter/bin/cache/pkg/sky_engine/
file:///D:/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///D:/flutter/packages/flutter/
file:///D:/flutter/packages/flutter/lib/
flutter_test
3.7
file:///D:/flutter/packages/flutter_test/
file:///D:/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///D:/flutter/packages/flutter_web_plugins/
file:///D:/flutter/packages/flutter_web_plugins/lib/
2
