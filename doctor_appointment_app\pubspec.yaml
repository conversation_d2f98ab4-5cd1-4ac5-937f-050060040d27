name: doctor_appointment_app
description: A comprehensive Flutter app for managing doctor appointments with communication features.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.8
  flutter_launcher_icons: ^0.13.1

  # Database
  sqflite: ^2.3.0
  path: ^1.8.3

  # HTTP and API
  http: ^1.1.0
  connectivity_plus: ^5.0.1

  # Communication
  url_launcher: ^6.2.1
  permission_handler: ^11.0.1

  # Notifications (temporarily removed for compatibility)
  # flutter_local_notifications: ^15.1.0
  # timezone: ^0.9.2

  # State Management and Navigation
  provider: ^6.1.1
  go_router: ^12.1.1

  # Date and Time
  intl: ^0.18.1

  # Shared Preferences
  shared_preferences: ^2.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
