import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user.dart';
import '../models/appointment.dart';
import '../models/report.dart';

class DatabaseService {
  static final DatabaseService instance = DatabaseService._init();
  static Database? _database;

  DatabaseService._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('doctor_appointment.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDB,
    );
  }

  Future _createDB(Database db, int version) async {
    const idType = 'INTEGER PRIMARY KEY AUTOINCREMENT';
    const textType = 'TEXT NOT NULL';
    const textTypeNullable = 'TEXT';
    const integerType = 'INTEGER NOT NULL';

    // Users table
    await db.execute('''
      CREATE TABLE users (
        id $idType,
        email $textType,
        password $textType,
        firstName $textType,
        lastName $textType,
        phone $textTypeNullable,
        dateOfBirth $textTypeNullable,
        address $textTypeNullable,
        emergencyContact $textTypeNullable,
        createdAt $textType
      )
    ''');

    // Appointments table
    await db.execute('''
      CREATE TABLE appointments (
        id $idType,
        userId $integerType,
        doctorName $textType,
        doctorSpecialty $textType,
        appointmentDate $textType,
        appointmentTime $textType,
        location $textType,
        notes $textTypeNullable,
        status $textType,
        reminderSet $integerType,
        createdAt $textType,
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    ''');

    // Reports table
    await db.execute('''
      CREATE TABLE reports (
        id $idType,
        userId $integerType,
        appointmentId $integerType,
        reportType $textType,
        title $textType,
        description $textType,
        reportDate $textType,
        attachments $textTypeNullable,
        createdAt $textType,
        FOREIGN KEY (userId) REFERENCES users (id),
        FOREIGN KEY (appointmentId) REFERENCES appointments (id)
      )
    ''');

    // Insert default admin user
    await db.insert('users', {
      'email': '<EMAIL>',
      'password': 'admin123',
      'firstName': 'Admin',
      'lastName': 'User',
      'phone': '+1234567890',
      'createdAt': DateTime.now().toIso8601String(),
    });
  }

  // User operations
  Future<User?> loginUser(String email, String password) async {
    try {
      final db = await instance.database;
      final maps = await db.query(
        'users',
        where: 'email = ? AND password = ?',
        whereArgs: [email, password],
      );

      if (maps.isNotEmpty) {
        return User.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Login failed: $e');
    }
  }

  Future<bool> emailExists(String email) async {
    try {
      final db = await instance.database;
      final maps = await db.query(
        'users',
        where: 'email = ?',
        whereArgs: [email],
      );
      return maps.isNotEmpty;
    } catch (e) {
      throw Exception('Email check failed: $e');
    }
  }

  Future<int> createUser(User user) async {
    final db = await instance.database;
    return await db.insert('users', user.toMap());
  }

  Future<User?> getUserById(int id) async {
    final db = await instance.database;
    final maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateUser(User user) async {
    final db = await instance.database;
    return await db.update(
      'users',
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  // Appointment operations
  Future<int> createAppointment(Appointment appointment) async {
    final db = await instance.database;
    return await db.insert('appointments', appointment.toMap());
  }

  Future<List<Appointment>> getAppointmentsByUserId(int userId) async {
    final db = await instance.database;
    final maps = await db.query(
      'appointments',
      where: 'userId = ?',
      whereArgs: [userId],
      orderBy: 'appointmentDate DESC, appointmentTime DESC',
    );

    return List.generate(maps.length, (i) => Appointment.fromMap(maps[i]));
  }

  Future<int> updateAppointment(Appointment appointment) async {
    final db = await instance.database;
    return await db.update(
      'appointments',
      appointment.toMap(),
      where: 'id = ?',
      whereArgs: [appointment.id],
    );
  }

  Future<int> deleteAppointment(int id) async {
    final db = await instance.database;
    return await db.delete(
      'appointments',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Report operations
  Future<int> createReport(Report report) async {
    final db = await instance.database;
    return await db.insert('reports', report.toMap());
  }

  Future<List<Report>> getReportsByUserId(int userId) async {
    final db = await instance.database;
    final maps = await db.query(
      'reports',
      where: 'userId = ?',
      whereArgs: [userId],
      orderBy: 'reportDate DESC',
    );

    return List.generate(maps.length, (i) => Report.fromMap(maps[i]));
  }

  Future<int> updateReport(Report report) async {
    final db = await instance.database;
    return await db.update(
      'reports',
      report.toMap(),
      where: 'id = ?',
      whereArgs: [report.id],
    );
  }

  Future<int> deleteReport(int id) async {
    final db = await instance.database;
    return await db.delete(
      'reports',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future close() async {
    final db = await instance.database;
    db.close();
  }
}
