// Temporarily disabled flutter_local_notifications for compatibility
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:timezone/timezone.dart' as tz;
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';

class NotificationService {
  // static final FlutterLocalNotificationsPlugin _notifications =
  //     FlutterLocalNotificationsPlugin();

  static Future<void> initialize() async {
    // Temporarily disabled - just return success
    debugPrint('NotificationService: Initialized (stub implementation)');
    return;

    // Request notification permissions
    // await Permission.notification.request();

    // const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    // const iosSettings = DarwinInitializationSettings(
    //   requestAlertPermission: true,
    //   requestBadgePermission: true,
    //   requestSoundPermission: true,
    // );

    // const initializationSettings = InitializationSettings(
    //   android: androidSettings,
    //   iOS: iosSettings,
    // );

    // await _notifications.initialize(
    //   initializationSettings,
    //   onDidReceiveNotificationResponse: _onNotificationTapped,
    // );
  }

  static void _onNotificationTapped(dynamic response) {
    // Handle notification tap (stub implementation)
    debugPrint('Notification tapped: $response');
  }

  static Future<void> showInstantNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    // Stub implementation - just log
    debugPrint('NotificationService: Would show notification - $title: $body');
    return;

    // const androidDetails = AndroidNotificationDetails(
    //   'instant_notifications',
    //   'Instant Notifications',
    //   channelDescription: 'Notifications that appear immediately',
    //   importance: Importance.high,
    //   priority: Priority.high,
    // );

    // const iosDetails = DarwinNotificationDetails(
    //   presentAlert: true,
    //   presentBadge: true,
    //   presentSound: true,
    // );

    // const notificationDetails = NotificationDetails(
    //   android: androidDetails,
    //   iOS: iosDetails,
    // );

    // await _notifications.show(
    //   id,
    //   title,
    //   body,
    //   notificationDetails,
    //   payload: payload,
    // );
  }

  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    // Stub implementation - just log
    debugPrint('NotificationService: Would schedule notification for $scheduledDate - $title: $body');
    return;
  }

  static Future<void> scheduleAppointmentReminder({
    required int appointmentId,
    required String doctorName,
    required DateTime appointmentDateTime,
  }) async {
    // Schedule reminder 1 hour before appointment
    final reminderTime = appointmentDateTime.subtract(const Duration(hours: 1));
    
    if (reminderTime.isAfter(DateTime.now())) {
      await scheduleNotification(
        id: appointmentId,
        title: 'Appointment Reminder',
        body: 'You have an appointment with Dr. $doctorName in 1 hour',
        scheduledDate: reminderTime,
        payload: 'appointment_$appointmentId',
      );
    }

    // Schedule reminder 15 minutes before appointment
    final urgentReminderTime = appointmentDateTime.subtract(const Duration(minutes: 15));
    
    if (urgentReminderTime.isAfter(DateTime.now())) {
      await scheduleNotification(
        id: appointmentId + 10000, // Different ID for urgent reminder
        title: 'Urgent: Appointment in 15 minutes',
        body: 'Your appointment with Dr. $doctorName starts in 15 minutes',
        scheduledDate: urgentReminderTime,
        payload: 'urgent_appointment_$appointmentId',
      );
    }
  }

  static Future<void> cancelNotification(int id) async {
    // Stub implementation - just log
    debugPrint('NotificationService: Would cancel notification $id');
    return;
  }

  static Future<void> cancelAllNotifications() async {
    // Stub implementation - just log
    debugPrint('NotificationService: Would cancel all notifications');
    return;
  }

  static Future<List<dynamic>> getPendingNotifications() async {
    // Stub implementation - return empty list
    debugPrint('NotificationService: Would get pending notifications');
    return [];
  }
}
