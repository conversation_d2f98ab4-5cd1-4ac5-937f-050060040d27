import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../services/device_search_service.dart';
import '../utils/app_theme.dart';

class DeviceSearchScreen extends StatefulWidget {
  const DeviceSearchScreen({super.key});

  @override
  State<DeviceSearchScreen> createState() => _DeviceSearchScreenState();
}

class _DeviceSearchScreenState extends State<DeviceSearchScreen> {
  final DeviceSearchService _deviceService = DeviceSearchService();
  List<HealthDevice> _devices = [];
  bool _isSearching = false;
  String? _selectedDeviceId;

  @override
  void initState() {
    super.initState();
    _setupDeviceStream();
  }

  void _setupDeviceStream() {
    _deviceService.devicesStream.listen((devices) {
      if (mounted) {
        setState(() {
          _devices = devices;
        });
      }
    });
  }

  Future<void> _startSearch() async {
    setState(() {
      _isSearching = true;
    });

    final success = await _deviceService.startDeviceSearch();
    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to start device search. Check permissions.'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isSearching = false;
      });
    }
  }

  Future<void> _stopSearch() async {
    await _deviceService.stopDeviceSearch();
    setState(() {
      _isSearching = false;
    });
  }

  Future<void> _connectToDevice(HealthDevice device) async {
    setState(() {
      _selectedDeviceId = device.id;
    });

    final success = await _deviceService.connectToDevice(device.id);
    
    setState(() {
      _selectedDeviceId = null;
    });

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Connected to ${device.name}'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to connect to ${device.name}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _disconnectFromDevice(HealthDevice device) async {
    await _deviceService.disconnectFromDevice(device.id);
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Disconnected from ${device.name}'),
        ),
      );
    }
  }

  Future<void> _getDeviceData(HealthDevice device) async {
    try {
      final data = await _deviceService.getDeviceData(device.id);
      if (data != null && mounted) {
        _showDeviceDataDialog(device, data);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No data available. Make sure device is connected.'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting device data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeviceDataDialog(HealthDevice device, Map<String, dynamic> data) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${device.name} Data'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              ...data.entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${entry.key}:',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    Text(entry.value.toString()),
                  ],
                ),
              )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // In a real app, you would save this data to the database
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Data saved to health records')),
              );
            },
            child: const Text('Save Data'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _deviceService.stopDeviceSearch();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Health Devices'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/dashboard'),
        ),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.stop : Icons.search),
            onPressed: _isSearching ? _stopSearch : _startSearch,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Status
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: _isSearching ? AppTheme.primaryColor.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
            child: Row(
              children: [
                if (_isSearching) ...[
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  const SizedBox(width: 12),
                  const Text('Searching for health devices...'),
                ] else ...[
                  Icon(
                    Icons.info_outline,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Found ${_devices.length} device(s). Tap search to find more.',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ],
            ),
          ),
          
          // Device List
          Expanded(
            child: _devices.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _devices.length,
                    itemBuilder: (context, index) {
                      final device = _devices[index];
                      return _buildDeviceCard(device);
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _isSearching ? _stopSearch : _startSearch,
        backgroundColor: _isSearching ? Colors.red : AppTheme.primaryColor,
        child: Icon(_isSearching ? Icons.stop : Icons.search),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.devices_outlined,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No health devices found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Make sure your devices are nearby and discoverable',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _startSearch,
            icon: const Icon(Icons.search),
            label: const Text('Start Search'),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceCard(HealthDevice device) {
    final isConnecting = _selectedDeviceId == device.id;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: device.isConnected 
              ? AppTheme.successColor 
              : Colors.grey.withOpacity(0.2),
          width: device.isConnected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  device.deviceIcon,
                  color: AppTheme.primaryColor,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      device.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      device.manufacturer,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.signal_cellular_alt,
                          size: 16,
                          color: _getSignalColor(device.signalStrength),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          device.signalStrengthText,
                          style: TextStyle(
                            fontSize: 12,
                            color: _getSignalColor(device.signalStrength),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (device.isConnected)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.successColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Connected',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.successColor,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Device Info
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Model: ${device.modelNumber}',
                      style: const TextStyle(fontSize: 12, color: AppTheme.textSecondary),
                    ),
                    Text(
                      'Firmware: ${device.firmwareVersion}',
                      style: const TextStyle(fontSize: 12, color: AppTheme.textSecondary),
                    ),
                    Text(
                      'Battery: ${device.batteryLevel}%',
                      style: const TextStyle(fontSize: 12, color: AppTheme.textSecondary),
                    ),
                  ],
                ),
              ),
              Text(
                'Last seen: ${DateFormat('HH:mm:ss').format(device.lastSeen)}',
                style: const TextStyle(fontSize: 12, color: AppTheme.textSecondary),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Capabilities
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: device.capabilities.take(3).map((capability) => 
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  capability,
                  style: const TextStyle(
                    fontSize: 10,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Action Buttons
          Row(
            children: [
              if (!device.isConnected) ...[
                Expanded(
                  child: ElevatedButton(
                    onPressed: isConnecting ? null : () => _connectToDevice(device),
                    child: isConnecting
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Connect'),
                  ),
                ),
              ] else ...[
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _disconnectFromDevice(device),
                    child: const Text('Disconnect'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _getDeviceData(device),
                    child: const Text('Get Data'),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Color _getSignalColor(int signalStrength) {
    if (signalStrength >= -50) return AppTheme.successColor;
    if (signalStrength >= -60) return AppTheme.accentColor;
    if (signalStrength >= -70) return AppTheme.warningColor;
    return Colors.red;
  }
}
