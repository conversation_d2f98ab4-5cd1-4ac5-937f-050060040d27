import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';

class ApiService {
  static const String _baseUrl = 'https://jsonplaceholder.typicode.com'; // Demo API
  static const Duration _timeout = Duration(seconds: 30);

  // Check internet connectivity
  static Future<bool> hasInternetConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  // Generic GET request
  static Future<Map<String, dynamic>?> _get(String endpoint) async {
    if (!await hasInternetConnection()) {
      throw Exception('No internet connection');
    }

    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl$endpoint'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Generic POST request
  static Future<Map<String, dynamic>?> _post(String endpoint, Map<String, dynamic> data) async {
    if (!await hasInternetConnection()) {
      throw Exception('No internet connection');
    }

    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl$endpoint'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode(data),
          )
          .timeout(_timeout);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return json.decode(response.body);
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Fetch doctor information (using demo API)
  static Future<List<Map<String, dynamic>>> fetchDoctors() async {
    try {
      // Using JSONPlaceholder users as demo doctors
      final response = await http
          .get(Uri.parse('$_baseUrl/users'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final List<dynamic> users = json.decode(response.body);
        
        // Transform users into doctor format
        return users.map((user) => {
          'id': user['id'],
          'name': 'Dr. ${user['name']}',
          'specialty': _getRandomSpecialty(),
          'phone': user['phone'],
          'email': user['email'],
          'hospital': '${user['company']['name']} Medical Center',
          'address': '${user['address']['street']}, ${user['address']['city']}',
          'available': (user['id'] % 3) != 0, // Random availability
          'rating': (4.0 + (user['id'] % 10) / 10).toStringAsFixed(1),
        }).toList();
      } else {
        throw Exception('Failed to fetch doctors');
      }
    } catch (e) {
      throw Exception('Error fetching doctors: $e');
    }
  }

  // Fetch clinic information
  static Future<List<Map<String, dynamic>>> fetchClinics() async {
    try {
      // Using JSONPlaceholder posts as demo clinics
      final response = await http
          .get(Uri.parse('$_baseUrl/posts?_limit=10'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final List<dynamic> posts = json.decode(response.body);
        
        return posts.map((post) => {
          'id': post['id'],
          'name': '${_getClinicName(post['id'])} Clinic',
          'description': post['body'],
          'address': '${post['id']} Medical Street, Health City',
          'phone': '+1234567${post['id'].toString().padLeft(3, '0')}',
          'email': 'info@clinic${post['id']}.com',
          'services': _getClinicServices(),
          'rating': (4.0 + (post['id'] % 10) / 10).toStringAsFixed(1),
          'openHours': '8:00 AM - 6:00 PM',
        }).toList();
      } else {
        throw Exception('Failed to fetch clinics');
      }
    } catch (e) {
      throw Exception('Error fetching clinics: $e');
    }
  }

  // Fetch health tips
  static Future<List<Map<String, dynamic>>> fetchHealthTips() async {
    try {
      // Using JSONPlaceholder comments as demo health tips
      final response = await http
          .get(Uri.parse('$_baseUrl/comments?_limit=20'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final List<dynamic> comments = json.decode(response.body);
        
        return comments.map((comment) => {
          'id': comment['id'],
          'title': _getHealthTipTitle(comment['id']),
          'content': comment['body'],
          'category': _getHealthTipCategory(comment['id']),
          'author': 'Dr. ${comment['name']}',
          'date': DateTime.now().subtract(Duration(days: comment['id'])).toIso8601String(),
        }).toList();
      } else {
        throw Exception('Failed to fetch health tips');
      }
    } catch (e) {
      throw Exception('Error fetching health tips: $e');
    }
  }

  // Submit appointment feedback
  static Future<bool> submitFeedback({
    required int appointmentId,
    required int rating,
    required String comment,
  }) async {
    try {
      final data = {
        'appointmentId': appointmentId,
        'rating': rating,
        'comment': comment,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await _post('/posts', data); // Demo endpoint
      return response != null;
    } catch (e) {
      throw Exception('Error submitting feedback: $e');
    }
  }

  // Helper methods for demo data
  static String _getRandomSpecialty() {
    final specialties = [
      'Cardiology',
      'Dermatology',
      'Pediatrics',
      'Orthopedics',
      'Neurology',
      'Psychiatry',
      'Oncology',
      'Gynecology',
      'Ophthalmology',
      'ENT',
    ];
    return specialties[DateTime.now().millisecond % specialties.length];
  }

  static String _getClinicName(int id) {
    final names = [
      'Central',
      'City',
      'General',
      'Community',
      'Family',
      'Wellness',
      'Care',
      'Health',
      'Medical',
      'Prime',
    ];
    return names[id % names.length];
  }

  static List<String> _getClinicServices() {
    final allServices = [
      'General Consultation',
      'Emergency Care',
      'Laboratory Tests',
      'X-Ray Services',
      'Pharmacy',
      'Vaccination',
      'Health Checkups',
      'Specialist Consultation',
    ];
    
    // Return random 3-5 services
    final count = 3 + (DateTime.now().millisecond % 3);
    allServices.shuffle();
    return allServices.take(count).toList();
  }

  static String _getHealthTipTitle(int id) {
    final titles = [
      'Stay Hydrated for Better Health',
      'The Importance of Regular Exercise',
      'Healthy Eating Habits',
      'Getting Quality Sleep',
      'Managing Stress Effectively',
      'Regular Health Checkups',
      'Benefits of Meditation',
      'Maintaining Good Posture',
      'Hand Hygiene Best Practices',
      'Mental Health Awareness',
    ];
    return titles[id % titles.length];
  }

  static String _getHealthTipCategory(int id) {
    final categories = [
      'Nutrition',
      'Exercise',
      'Mental Health',
      'Preventive Care',
      'Lifestyle',
    ];
    return categories[id % categories.length];
  }
}
