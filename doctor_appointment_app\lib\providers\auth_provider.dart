import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/user.dart';
import '../services/database_service.dart';

class AuthProvider with ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _currentUser != null;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  // Validation methods
  String? validateEmail(String email) {
    if (email.isEmpty) return 'Email is required';
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email)) return 'Please enter a valid email';
    return null;
  }

  String? validatePassword(String password) {
    if (password.isEmpty) return 'Password is required';
    if (password.length < 6) return 'Password must be at least 6 characters';
    return null;
  }

  String? validateName(String name) {
    if (name.isEmpty) return 'This field is required';
    if (name.length < 2) return 'Name must be at least 2 characters';
    return null;
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _setError(null);

    // Validate input
    final emailError = validateEmail(email);
    final passwordError = validatePassword(password);

    if (emailError != null) {
      _setError(emailError);
      _setLoading(false);
      return false;
    }

    if (passwordError != null) {
      _setError(passwordError);
      _setLoading(false);
      return false;
    }

    try {
      final user = await DatabaseService.instance.loginUser(email.trim().toLowerCase(), password);
      if (user != null) {
        _currentUser = user;
        await _saveUserSession(user.id!);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Invalid email or password');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
    String? dateOfBirth,
    String? address,
    String? emergencyContact,
  }) async {
    _setLoading(true);
    _setError(null);

    // Validate input
    final emailError = validateEmail(email);
    final passwordError = validatePassword(password);
    final firstNameError = validateName(firstName);
    final lastNameError = validateName(lastName);

    if (emailError != null) {
      _setError(emailError);
      _setLoading(false);
      return false;
    }

    if (passwordError != null) {
      _setError(passwordError);
      _setLoading(false);
      return false;
    }

    if (firstNameError != null) {
      _setError('First name: $firstNameError');
      _setLoading(false);
      return false;
    }

    if (lastNameError != null) {
      _setError('Last name: $lastNameError');
      _setLoading(false);
      return false;
    }

    try {
      // Check if email already exists
      final emailExists = await DatabaseService.instance.emailExists(email.trim().toLowerCase());
      if (emailExists) {
        _setError('An account with this email already exists');
        _setLoading(false);
        return false;
      }

      final user = User(
        email: email.trim().toLowerCase(),
        password: password,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        phone: phone?.trim(),
        dateOfBirth: dateOfBirth,
        address: address?.trim(),
        emergencyContact: emergencyContact?.trim(),
        createdAt: DateTime.now().toIso8601String(),
      );

      final userId = await DatabaseService.instance.createUser(user);
      if (userId > 0) {
        _currentUser = user.copyWith(id: userId);
        await _saveUserSession(userId);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Registration failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateProfile({
    required String firstName,
    required String lastName,
    String? phone,
    String? dateOfBirth,
    String? address,
    String? emergencyContact,
  }) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _setError(null);

    try {
      final updatedUser = _currentUser!.copyWith(
        firstName: firstName,
        lastName: lastName,
        phone: phone,
        dateOfBirth: dateOfBirth,
        address: address,
        emergencyContact: emergencyContact,
      );

      final result = await DatabaseService.instance.updateUser(updatedUser);
      if (result > 0) {
        _currentUser = updatedUser;
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Profile update failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Profile update failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    await _clearUserSession();
    notifyListeners();
  }

  Future<void> checkAuthStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getInt('user_id');
    
    if (userId != null) {
      try {
        final user = await DatabaseService.instance.getUserById(userId);
        if (user != null) {
          _currentUser = user;
          notifyListeners();
        }
      } catch (e) {
        await _clearUserSession();
      }
    }
  }

  Future<void> _saveUserSession(int userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('user_id', userId);
  }

  Future<void> _clearUserSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_id');
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
