import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../providers/auth_provider.dart';
import '../providers/appointment_provider.dart';
import '../utils/app_theme.dart';

class ReportScreen extends StatefulWidget {
  const ReportScreen({super.key});

  @override
  State<ReportScreen> createState() => _ReportScreenState();
}

class _ReportScreenState extends State<ReportScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String _selectedReportType = 'Medical Report';
  DateTime? _selectedDate;
  int? _selectedAppointmentId;

  final List<String> _reportTypes = [
    'Medical Report',
    'Lab Results',
    'Prescription',
    'X-Ray',
    'Blood Test',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadReports();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _loadReports() {
    final authProvider = context.read<AuthProvider>();
    if (authProvider.currentUser != null) {
      context.read<AppointmentProvider>().loadReports(authProvider.currentUser!.id!);
      context.read<AppointmentProvider>().loadAppointments(authProvider.currentUser!.id!);
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _createReport() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a date')),
      );
      return;
    }

    final authProvider = context.read<AuthProvider>();
    final appointmentProvider = context.read<AppointmentProvider>();

    final success = await appointmentProvider.createReport(
      userId: authProvider.currentUser!.id!,
      appointmentId: _selectedAppointmentId,
      reportType: _selectedReportType,
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      reportDate: _selectedDate!,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Report created successfully!')),
      );
      _clearForm();
      _tabController.animateTo(1); // Switch to reports list tab
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(appointmentProvider.errorMessage ?? 'Failed to create report'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _clearForm() {
    _titleController.clear();
    _descriptionController.clear();
    setState(() {
      _selectedReportType = 'Medical Report';
      _selectedDate = null;
      _selectedAppointmentId = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Medical Reports'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/dashboard'),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Add Report', icon: Icon(Icons.add_circle_outline)),
            Tab(text: 'My Reports', icon: Icon(Icons.description)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAddReportTab(),
          _buildReportsListTab(),
        ],
      ),
    );
  }

  Widget _buildAddReportTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Add New Report',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 24),
            
            // Report Type
            DropdownButtonFormField<String>(
              value: _selectedReportType,
              decoration: const InputDecoration(
                labelText: 'Report Type',
                prefixIcon: Icon(Icons.category_outlined),
              ),
              items: _reportTypes.map((type) => DropdownMenuItem(
                value: type,
                child: Text(type),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedReportType = value!;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Title
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Report Title',
                prefixIcon: Icon(Icons.title_outlined),
                hintText: 'Enter report title',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // Date
            InkWell(
              onTap: _selectDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Report Date',
                  prefixIcon: Icon(Icons.calendar_today_outlined),
                ),
                child: Text(
                  _selectedDate == null
                      ? 'Select date'
                      : DateFormat('MMM dd, yyyy').format(_selectedDate!),
                  style: TextStyle(
                    color: _selectedDate == null ? Colors.grey : Colors.black,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Related Appointment (Optional)
            Consumer<AppointmentProvider>(
              builder: (context, appointmentProvider, child) {
                final appointments = appointmentProvider.appointments;
                
                return DropdownButtonFormField<int?>(
                  value: _selectedAppointmentId,
                  decoration: const InputDecoration(
                    labelText: 'Related Appointment (Optional)',
                    prefixIcon: Icon(Icons.event_outlined),
                  ),
                  items: [
                    const DropdownMenuItem<int?>(
                      value: null,
                      child: Text('No related appointment'),
                    ),
                    ...appointments.map((appointment) => DropdownMenuItem<int?>(
                      value: appointment.id,
                      child: Text('Dr. ${appointment.doctorName} - ${appointment.formattedDate}'),
                    )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedAppointmentId = value;
                    });
                  },
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // Description
            TextFormField(
              controller: _descriptionController,
              maxLines: 5,
              decoration: const InputDecoration(
                labelText: 'Description',
                prefixIcon: Icon(Icons.description_outlined),
                hintText: 'Enter detailed description of the report',
                alignLabelWithHint: true,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a description';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 32),
            
            // Create Button
            Consumer<AppointmentProvider>(
              builder: (context, appointmentProvider, child) {
                return SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: appointmentProvider.isLoading ? null : _createReport,
                    child: appointmentProvider.isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text('Create Report'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsListTab() {
    return Consumer<AppointmentProvider>(
      builder: (context, appointmentProvider, child) {
        final reports = appointmentProvider.reports;
        
        if (appointmentProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (reports.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.description_outlined,
                  size: 64,
                  color: Colors.grey.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'No reports available',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => _tabController.animateTo(0),
                  child: const Text('Add Report'),
                ),
              ],
            ),
          );
        }
        
        return RefreshIndicator(
          onRefresh: () async => _loadReports(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: reports.length,
            itemBuilder: (context, index) {
              final report = reports[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.withOpacity(0.2)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: _getReportTypeColor(report.reportType).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            _getReportTypeIcon(report.reportType),
                            color: _getReportTypeColor(report.reportType),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                report.title,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: _getReportTypeColor(report.reportType).withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  report.reportType,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: _getReportTypeColor(report.reportType),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          report.formattedDate,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Text(
                      report.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              _showReportDetails(report);
                            },
                            icon: const Icon(Icons.visibility_outlined, size: 16),
                            label: const Text('View Details'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Share feature coming soon')),
                              );
                            },
                            icon: const Icon(Icons.share_outlined, size: 16),
                            label: const Text('Share'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Color _getReportTypeColor(String reportType) {
    switch (reportType.toLowerCase()) {
      case 'medical report':
        return AppTheme.primaryColor;
      case 'lab results':
        return AppTheme.accentColor;
      case 'prescription':
        return AppTheme.warningColor;
      case 'x-ray':
        return Colors.purple;
      case 'blood test':
        return Colors.red;
      default:
        return AppTheme.textSecondary;
    }
  }

  IconData _getReportTypeIcon(String reportType) {
    switch (reportType.toLowerCase()) {
      case 'medical report':
        return Icons.medical_services_outlined;
      case 'lab results':
        return Icons.science_outlined;
      case 'prescription':
        return Icons.medication_outlined;
      case 'x-ray':
        return Icons.camera_outlined;
      case 'blood test':
        return Icons.bloodtype_outlined;
      default:
        return Icons.description_outlined;
    }
  }

  void _showReportDetails(report) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(report.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(
                    _getReportTypeIcon(report.reportType),
                    color: _getReportTypeColor(report.reportType),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    report.reportType,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: _getReportTypeColor(report.reportType),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 16, color: AppTheme.textSecondary),
                  const SizedBox(width: 8),
                  Text(
                    report.formattedDate,
                    style: const TextStyle(color: AppTheme.textSecondary),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'Description:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              Text(report.description),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Download feature coming soon')),
              );
            },
            child: const Text('Download'),
          ),
        ],
      ),
    );
  }
}
