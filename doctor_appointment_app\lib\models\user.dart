class User {
  final int? id;
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final String? phone;
  final String? dateOfBirth;
  final String? address;
  final String? emergencyContact;
  final String createdAt;

  User({
    this.id,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.phone,
    this.dateOfBirth,
    this.address,
    this.emergencyContact,
    required this.createdAt,
  });

  String get fullName => '$firstName $lastName';

  String get initials => '${firstName.isNotEmpty ? firstName[0] : ''}${lastName.isNotEmpty ? lastName[0] : ''}';

  bool get hasCompleteProfile => phone != null && dateOfBirth != null && address != null;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'password': password,
      'firstName': firstName,
      'lastName': lastName,
      'phone': phone,
      'dateOfBirth': dateOfBirth,
      'address': address,
      'emergencyContact': emergencyContact,
      'createdAt': createdAt,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id']?.toInt(),
      email: map['email'] ?? '',
      password: map['password'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      phone: map['phone'],
      dateOfBirth: map['dateOfBirth'],
      address: map['address'],
      emergencyContact: map['emergencyContact'],
      createdAt: map['createdAt'] ?? '',
    );
  }

  User copyWith({
    int? id,
    String? email,
    String? password,
    String? firstName,
    String? lastName,
    String? phone,
    String? dateOfBirth,
    String? address,
    String? emergencyContact,
    String? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      password: password ?? this.password,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      address: address ?? this.address,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, email: $email, firstName: $firstName, lastName: $lastName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id && other.email == email;
  }

  @override
  int get hashCode {
    return id.hashCode ^ email.hashCode;
  }
}
