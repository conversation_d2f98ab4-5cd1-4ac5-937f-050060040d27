class Report {
  final int? id;
  final int userId;
  final int? appointmentId;
  final String reportType; // medical, lab, prescription, etc.
  final String title;
  final String description;
  final String reportDate; // YYYY-MM-DD format
  final String? attachments; // JSON string of file paths
  final String createdAt;

  Report({
    this.id,
    required this.userId,
    this.appointmentId,
    required this.reportType,
    required this.title,
    required this.description,
    required this.reportDate,
    this.attachments,
    required this.createdAt,
  });

  DateTime get reportDateTime {
    return DateTime.parse(reportDate);
  }

  String get formattedDate {
    final date = DateTime.parse(reportDate);
    return '${date.day}/${date.month}/${date.year}';
  }

  List<String> get attachmentList {
    if (attachments == null || attachments!.isEmpty) return [];
    // Simple implementation - in real app, you'd parse JSON
    return attachments!.split(',').where((s) => s.isNotEmpty).toList();
  }

  String get reportTypeDisplayText {
    switch (reportType.toLowerCase()) {
      case 'medical':
        return 'Medical Report';
      case 'lab':
        return 'Lab Results';
      case 'prescription':
        return 'Prescription';
      case 'xray':
        return 'X-Ray';
      case 'blood_test':
        return 'Blood Test';
      case 'scan':
        return 'Medical Scan';
      default:
        return reportType;
    }
  }

  bool get hasAttachments => attachmentList.isNotEmpty;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'appointmentId': appointmentId,
      'reportType': reportType,
      'title': title,
      'description': description,
      'reportDate': reportDate,
      'attachments': attachments,
      'createdAt': createdAt,
    };
  }

  factory Report.fromMap(Map<String, dynamic> map) {
    return Report(
      id: map['id']?.toInt(),
      userId: map['userId']?.toInt() ?? 0,
      appointmentId: map['appointmentId']?.toInt(),
      reportType: map['reportType'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      reportDate: map['reportDate'] ?? '',
      attachments: map['attachments'],
      createdAt: map['createdAt'] ?? '',
    );
  }

  Report copyWith({
    int? id,
    int? userId,
    int? appointmentId,
    String? reportType,
    String? title,
    String? description,
    String? reportDate,
    String? attachments,
    String? createdAt,
  }) {
    return Report(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      appointmentId: appointmentId ?? this.appointmentId,
      reportType: reportType ?? this.reportType,
      title: title ?? this.title,
      description: description ?? this.description,
      reportDate: reportDate ?? this.reportDate,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Report(id: $id, title: $title, type: $reportType, date: $reportDate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Report && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
