1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.doctor_appointment_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:4:5-67
15-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:4:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:5:5-79
16-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:5:22-76
17    <uses-permission android:name="android.permission.CALL_PHONE" />
17-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:6:5-69
17-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:6:22-66
18    <uses-permission android:name="android.permission.SEND_SMS" />
18-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:7:5-67
18-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:7:22-64
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:8:5-66
19-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:8:22-63
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:9:5-81
20-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:9:22-78
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:10:5-68
21-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:10:22-65
22    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
22-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:11:5-79
22-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:11:22-76
23    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
23-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:12:5-74
23-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:12:22-71
24    <!--
25 Required to query activities that can process text, see:
26         https://developer.android.com/training/package-visibility and
27         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
28
29         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
30    -->
31    <queries>
31-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:51:5-56:15
32        <intent>
32-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:52:9-55:18
33            <action android:name="android.intent.action.PROCESS_TEXT" />
33-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:53:13-72
33-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:53:21-70
34
35            <data android:mimeType="text/plain" />
35-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:54:13-50
35-->D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\AndroidManifest.xml:54:19-48
36        </intent>
37    </queries>
38
39    <permission
39-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
40        android:name="com.example.doctor_appointment_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
40-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
41        android:protectionLevel="signature" />
41-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
42
43    <uses-permission android:name="com.example.doctor_appointment_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
43-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
43-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
44
45    <application
46        android:name="android.app.Application"
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:debuggable="true"
49        android:extractNativeLibs="true"
50        android:icon="@mipmap/ic_launcher"
51        android:label="doctor_appointment_app" >
52        <activity
53            android:name="com.example.doctor_appointment_app.MainActivity"
54            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
55            android:exported="true"
56            android:hardwareAccelerated="true"
57            android:launchMode="singleTop"
58            android:taskAffinity=""
59            android:theme="@style/LaunchTheme"
60            android:windowSoftInputMode="adjustResize" >
61
62            <!--
63                 Specifies an Android theme to apply to this Activity as soon as
64                 the Android process has started. This theme is visible to the user
65                 while the Flutter UI initializes. After that, this theme continues
66                 to determine the Window background behind the Flutter UI.
67            -->
68            <meta-data
69                android:name="io.flutter.embedding.android.NormalTheme"
70                android:resource="@style/NormalTheme" />
71
72            <intent-filter>
73                <action android:name="android.intent.action.MAIN" />
74
75                <category android:name="android.intent.category.LAUNCHER" />
76            </intent-filter>
77        </activity>
78        <!--
79             Don't delete the meta-data below.
80             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
81        -->
82        <meta-data
83            android:name="flutterEmbedding"
84            android:value="2" />
85
86        <activity
86-->[:url_launcher_android] D:\Doctor Appointment App\doctor_appointment_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
87            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
87-->[:url_launcher_android] D:\Doctor Appointment App\doctor_appointment_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
88            android:exported="false"
88-->[:url_launcher_android] D:\Doctor Appointment App\doctor_appointment_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
89            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
89-->[:url_launcher_android] D:\Doctor Appointment App\doctor_appointment_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
90
91        <uses-library
91-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
92            android:name="androidx.window.extensions"
92-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
93            android:required="false" />
93-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
94        <uses-library
94-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
95            android:name="androidx.window.sidecar"
95-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
96            android:required="false" />
96-->[androidx.window:window:1.2.0] D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
97
98        <provider
98-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
99            android:name="androidx.startup.InitializationProvider"
99-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
100            android:authorities="com.example.doctor_appointment_app.androidx-startup"
100-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
101            android:exported="false" >
101-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
102            <meta-data
102-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
103                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
103-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
104                android:value="androidx.startup" />
104-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
105            <meta-data
105-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
106                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
106-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
107                android:value="androidx.startup" />
107-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
108        </provider>
109
110        <receiver
110-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
111            android:name="androidx.profileinstaller.ProfileInstallReceiver"
111-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
112            android:directBootAware="false"
112-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
113            android:enabled="true"
113-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
114            android:exported="true"
114-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
115            android:permission="android.permission.DUMP" >
115-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
116            <intent-filter>
116-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
117                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
117-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
117-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
118            </intent-filter>
119            <intent-filter>
119-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
120                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
120-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
120-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
121            </intent-filter>
122            <intent-filter>
122-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
123                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
123-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
123-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
124            </intent-filter>
125            <intent-filter>
125-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
126                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
126-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
126-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
127            </intent-filter>
128        </receiver>
129    </application>
130
131</manifest>
