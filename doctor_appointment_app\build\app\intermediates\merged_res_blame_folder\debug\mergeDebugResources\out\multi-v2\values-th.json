{"logs": [{"outputFile": "com.example.doctor_appointment_app-mergeDebugResources-4:/values-th/values-th.xml", "map": [{"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3519,3708,3807,3918", "endColumns": "102,98,110,97", "endOffsets": "3617,3802,3913,4011"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,864,955,1047,1138,1232,1333,1426,1521,1615,1706,1797,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,77,90,91,90,93,100,92,94,93,90,90,79,102,97,97,102,105,100,152,94,80", "endOffsets": "205,298,406,491,593,703,781,859,950,1042,1133,1227,1328,1421,1516,1610,1701,1792,1872,1975,2073,2171,2274,2380,2481,2634,2729,2810"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,864,955,1047,1138,1232,1333,1426,1521,1615,1706,1797,1877,1980,2078,2176,2279,2385,2486,2639,4235", "endColumns": "104,92,107,84,101,109,77,77,90,91,90,93,100,92,94,93,90,90,79,102,97,97,102,105,100,152,94,80", "endOffsets": "205,298,406,491,593,703,781,859,950,1042,1133,1227,1328,1421,1516,1610,1701,1792,1872,1975,2073,2171,2274,2380,2481,2634,2729,4311"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "29,30,31,32,33,34,35,45", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2734,2830,2933,3031,3129,3232,3337,4316", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "2825,2928,3026,3124,3227,3332,3444,4412"}}, {"source": "D:\\FlutterCache\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "36,38,42,43,46,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3449,3622,4016,4096,4417,4585,4665", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "3514,3703,4091,4230,4580,4660,4738"}}]}]}