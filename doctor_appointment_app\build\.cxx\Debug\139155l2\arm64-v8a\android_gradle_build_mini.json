{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Doctor Appointment App\\doctor_appointment_app\\build\\.cxx\\Debug\\139155l2\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Doctor Appointment App\\doctor_appointment_app\\build\\.cxx\\Debug\\139155l2\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}