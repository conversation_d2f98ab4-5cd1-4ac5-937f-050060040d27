com.example.doctor_appointment_app-debug-0 D:\Doctor Appointment App\doctor_appointment_app\android\app\src\debug\res
com.example.doctor_appointment_app-main-1 D:\Doctor Appointment App\doctor_appointment_app\android\app\src\main\res
com.example.doctor_appointment_app-pngs-2 D:\Doctor Appointment App\doctor_appointment_app\build\app\generated\res\pngs\debug
com.example.doctor_appointment_app-resValues-3 D:\Doctor Appointment App\doctor_appointment_app\build\app\generated\res\resValues\debug
com.example.doctor_appointment_app-packageDebugResources-4 D:\Doctor Appointment App\doctor_appointment_app\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.doctor_appointment_app-packageDebugResources-5 D:\Doctor Appointment App\doctor_appointment_app\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.doctor_appointment_app-debug-6 D:\Doctor Appointment App\doctor_appointment_app\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.doctor_appointment_app-debug-7 D:\Doctor Appointment App\doctor_appointment_app\build\connectivity_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.doctor_appointment_app-debug-8 D:\Doctor Appointment App\doctor_appointment_app\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
com.example.doctor_appointment_app-debug-9 D:\Doctor Appointment App\doctor_appointment_app\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.example.doctor_appointment_app-debug-10 D:\Doctor Appointment App\doctor_appointment_app\build\sqflite_android\intermediates\packaged_res\debug\packageDebugResources
com.example.doctor_appointment_app-debug-11 D:\Doctor Appointment App\doctor_appointment_app\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
com.example.doctor_appointment_app-browser-1.8.0-12 D:\FlutterCache\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\res
com.example.doctor_appointment_app-jetified-lifecycle-process-2.7.0-13 D:\FlutterCache\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\res
com.example.doctor_appointment_app-appcompat-1.1.0-14 D:\FlutterCache\.gradle\caches\8.12\transforms\0d0ad2c9a7eee0ad2b557032bddebd70\transformed\appcompat-1.1.0\res
com.example.doctor_appointment_app-jetified-window-1.2.0-15 D:\FlutterCache\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\res
com.example.doctor_appointment_app-fragment-1.7.1-16 D:\FlutterCache\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\res
com.example.doctor_appointment_app-core-runtime-2.2.0-17 D:\FlutterCache\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\res
com.example.doctor_appointment_app-lifecycle-livedata-core-2.7.0-18 D:\FlutterCache\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\res
com.example.doctor_appointment_app-jetified-lifecycle-viewmodel-savedstate-2.7.0-19 D:\FlutterCache\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.doctor_appointment_app-jetified-annotation-experimental-1.4.0-20 D:\FlutterCache\.gradle\caches\8.12\transforms\25a37e5cf0f4e5220cbf7cafe9249990\transformed\jetified-annotation-experimental-1.4.0\res
com.example.doctor_appointment_app-jetified-tracing-1.2.0-21 D:\FlutterCache\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\res
com.example.doctor_appointment_app-coordinatorlayout-1.0.0-22 D:\FlutterCache\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\res
com.example.doctor_appointment_app-jetified-lifecycle-viewmodel-ktx-2.7.0-23 D:\FlutterCache\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.doctor_appointment_app-jetified-datastore-core-release-24 D:\FlutterCache\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\res
com.example.doctor_appointment_app-jetified-savedstate-ktx-1.2.1-25 D:\FlutterCache\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.doctor_appointment_app-jetified-core-1.0.0-26 D:\FlutterCache\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\res
com.example.doctor_appointment_app-jetified-datastore-release-27 D:\FlutterCache\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\res
com.example.doctor_appointment_app-jetified-window-java-1.2.0-28 D:\FlutterCache\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\res
com.example.doctor_appointment_app-core-1.13.1-29 D:\FlutterCache\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\res
com.example.doctor_appointment_app-jetified-datastore-preferences-release-30 D:\FlutterCache\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\res
com.example.doctor_appointment_app-recyclerview-1.0.0-31 D:\FlutterCache\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\res
com.example.doctor_appointment_app-jetified-savedstate-1.2.1-32 D:\FlutterCache\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\res
com.example.doctor_appointment_app-jetified-activity-ktx-1.8.1-33 D:\FlutterCache\.gradle\caches\8.12\transforms\84a8a15495959e34577fc85968c66982\transformed\jetified-activity-ktx-1.8.1\res
com.example.doctor_appointment_app-lifecycle-viewmodel-2.7.0-34 D:\FlutterCache\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\res
com.example.doctor_appointment_app-jetified-startup-runtime-1.1.1-35 D:\FlutterCache\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\res
com.example.doctor_appointment_app-jetified-fragment-ktx-1.7.1-36 D:\FlutterCache\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\res
com.example.doctor_appointment_app-jetified-profileinstaller-1.3.1-37 D:\FlutterCache\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\res
com.example.doctor_appointment_app-lifecycle-runtime-2.7.0-38 D:\FlutterCache\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\res
com.example.doctor_appointment_app-preference-1.2.1-39 D:\FlutterCache\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\res
com.example.doctor_appointment_app-jetified-lifecycle-runtime-ktx-2.7.0-40 D:\FlutterCache\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.doctor_appointment_app-jetified-lifecycle-livedata-core-ktx-2.7.0-41 D:\FlutterCache\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.doctor_appointment_app-jetified-activity-1.8.1-42 D:\FlutterCache\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\res
com.example.doctor_appointment_app-lifecycle-livedata-2.7.0-43 D:\FlutterCache\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0\res
com.example.doctor_appointment_app-jetified-core-ktx-1.13.1-44 D:\FlutterCache\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\res
com.example.doctor_appointment_app-jetified-appcompat-resources-1.1.0-45 D:\FlutterCache\.gradle\caches\8.12\transforms\e93556932885008eff7df21847fbdad2\transformed\jetified-appcompat-resources-1.1.0\res
com.example.doctor_appointment_app-slidingpanelayout-1.2.0-46 D:\FlutterCache\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\res
com.example.doctor_appointment_app-transition-1.4.1-47 D:\FlutterCache\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\res
