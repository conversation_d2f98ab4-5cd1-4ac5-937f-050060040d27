class Appointment {
  final int? id;
  final int userId;
  final String doctorName;
  final String doctorSpecialty;
  final String appointmentDate; // YYYY-MM-DD format
  final String appointmentTime; // HH:MM format
  final String location;
  final String? notes;
  final String status; // scheduled, completed, cancelled
  final bool reminderSet;
  final String createdAt;

  Appointment({
    this.id,
    required this.userId,
    required this.doctorName,
    required this.doctorSpecialty,
    required this.appointmentDate,
    required this.appointmentTime,
    required this.location,
    this.notes,
    this.status = 'scheduled',
    this.reminderSet = false,
    required this.createdAt,
  });

  DateTime get appointmentDateTime {
    return DateTime.parse('$appointmentDate $appointmentTime:00');
  }

  bool get isUpcoming {
    return appointmentDateTime.isAfter(DateTime.now()) && status == 'scheduled';
  }

  bool get isPast {
    return appointmentDateTime.isBefore(DateTime.now());
  }

  String get formattedDate {
    final date = DateTime.parse(appointmentDate);
    return '${date.day}/${date.month}/${date.year}';
  }

  String get formattedTime {
    final time = appointmentTime.split(':');
    final hour = int.parse(time[0]);
    final minute = time[1];
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }

  String get statusDisplayText {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return 'Scheduled';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'rescheduled':
        return 'Rescheduled';
      default:
        return status;
    }
  }

  Duration get timeUntilAppointment {
    return appointmentDateTime.difference(DateTime.now());
  }

  bool get isToday {
    final now = DateTime.now();
    final appointmentDay = DateTime.parse(appointmentDate);
    return now.year == appointmentDay.year &&
           now.month == appointmentDay.month &&
           now.day == appointmentDay.day;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'doctorName': doctorName,
      'doctorSpecialty': doctorSpecialty,
      'appointmentDate': appointmentDate,
      'appointmentTime': appointmentTime,
      'location': location,
      'notes': notes,
      'status': status,
      'reminderSet': reminderSet ? 1 : 0,
      'createdAt': createdAt,
    };
  }

  factory Appointment.fromMap(Map<String, dynamic> map) {
    return Appointment(
      id: map['id']?.toInt(),
      userId: map['userId']?.toInt() ?? 0,
      doctorName: map['doctorName'] ?? '',
      doctorSpecialty: map['doctorSpecialty'] ?? '',
      appointmentDate: map['appointmentDate'] ?? '',
      appointmentTime: map['appointmentTime'] ?? '',
      location: map['location'] ?? '',
      notes: map['notes'],
      status: map['status'] ?? 'scheduled',
      reminderSet: (map['reminderSet'] ?? 0) == 1,
      createdAt: map['createdAt'] ?? '',
    );
  }

  Appointment copyWith({
    int? id,
    int? userId,
    String? doctorName,
    String? doctorSpecialty,
    String? appointmentDate,
    String? appointmentTime,
    String? location,
    String? notes,
    String? status,
    bool? reminderSet,
    String? createdAt,
  }) {
    return Appointment(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      doctorName: doctorName ?? this.doctorName,
      doctorSpecialty: doctorSpecialty ?? this.doctorSpecialty,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      appointmentTime: appointmentTime ?? this.appointmentTime,
      location: location ?? this.location,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      reminderSet: reminderSet ?? this.reminderSet,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Appointment(id: $id, doctorName: $doctorName, date: $appointmentDate, time: $appointmentTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Appointment && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
