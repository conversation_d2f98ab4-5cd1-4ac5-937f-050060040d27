import 'package:flutter/foundation.dart';
import '../models/appointment.dart';
import '../models/report.dart';
import '../services/database_service.dart';
import '../services/notification_service.dart';

class AppointmentProvider with ChangeNotifier {
  List<Appointment> _appointments = [];
  List<Report> _reports = [];
  bool _isLoading = false;
  String? _errorMessage;

  List<Appointment> get appointments => _appointments;
  List<Report> get reports => _reports;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  List<Appointment> get upcomingAppointments {
    return _appointments.where((apt) => apt.isUpcoming).toList();
  }

  List<Appointment> get pastAppointments {
    return _appointments.where((apt) => apt.isPast).toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  Future<void> loadAppointments(int userId) async {
    _setLoading(true);
    _setError(null);

    try {
      _appointments = await DatabaseService.instance.getAppointmentsByUserId(userId);
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load appointments: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<bool> createAppointment({
    required int userId,
    required String doctorName,
    required String doctorSpecialty,
    required DateTime appointmentDate,
    required String location,
    String? notes,
    bool setReminder = true,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final appointment = Appointment(
        userId: userId,
        doctorName: doctorName,
        doctorSpecialty: doctorSpecialty,
        appointmentDate: appointmentDate.toIso8601String().split('T')[0],
        appointmentTime: '${appointmentDate.hour.toString().padLeft(2, '0')}:${appointmentDate.minute.toString().padLeft(2, '0')}',
        location: location,
        notes: notes,
        reminderSet: setReminder,
        createdAt: DateTime.now().toIso8601String(),
      );

      final appointmentId = await DatabaseService.instance.createAppointment(appointment);
      
      if (appointmentId > 0) {
        // Schedule notification if reminder is set
        if (setReminder) {
          await NotificationService.scheduleAppointmentReminder(
            appointmentId: appointmentId,
            doctorName: doctorName,
            appointmentDateTime: appointmentDate,
          );
        }

        // Reload appointments
        await loadAppointments(userId);
        _setLoading(false);
        return true;
      } else {
        _setError('Failed to create appointment');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to create appointment: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateAppointment(Appointment appointment) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await DatabaseService.instance.updateAppointment(appointment);
      
      if (result > 0) {
        // Update local list
        final index = _appointments.indexWhere((apt) => apt.id == appointment.id);
        if (index != -1) {
          _appointments[index] = appointment;
        }
        
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Failed to update appointment');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to update appointment: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> cancelAppointment(int appointmentId) async {
    _setLoading(true);
    _setError(null);

    try {
      final appointment = _appointments.firstWhere((apt) => apt.id == appointmentId);
      final updatedAppointment = appointment.copyWith(status: 'cancelled');
      
      final result = await DatabaseService.instance.updateAppointment(updatedAppointment);
      
      if (result > 0) {
        // Cancel notifications
        await NotificationService.cancelNotification(appointmentId);
        await NotificationService.cancelNotification(appointmentId + 10000);
        
        // Update local list
        final index = _appointments.indexWhere((apt) => apt.id == appointmentId);
        if (index != -1) {
          _appointments[index] = updatedAppointment;
        }
        
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('Failed to cancel appointment');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to cancel appointment: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> loadReports(int userId) async {
    _setLoading(true);
    _setError(null);

    try {
      _reports = await DatabaseService.instance.getReportsByUserId(userId);
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load reports: ${e.toString()}');
      _setLoading(false);
    }
  }

  Future<bool> createReport({
    required int userId,
    int? appointmentId,
    required String reportType,
    required String title,
    required String description,
    required DateTime reportDate,
    String? attachments,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final report = Report(
        userId: userId,
        appointmentId: appointmentId,
        reportType: reportType,
        title: title,
        description: description,
        reportDate: reportDate.toIso8601String().split('T')[0],
        attachments: attachments,
        createdAt: DateTime.now().toIso8601String(),
      );

      final reportId = await DatabaseService.instance.createReport(report);
      
      if (reportId > 0) {
        await loadReports(userId);
        _setLoading(false);
        return true;
      } else {
        _setError('Failed to create report');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to create report: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
