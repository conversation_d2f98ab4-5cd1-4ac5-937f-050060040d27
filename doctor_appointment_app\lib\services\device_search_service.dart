import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class DeviceSearchService {
  static final DeviceSearchService _instance = DeviceSearchService._internal();
  factory DeviceSearchService() => _instance;
  DeviceSearchService._internal();

  final StreamController<List<HealthDevice>> _devicesController = 
      StreamController<List<HealthDevice>>.broadcast();
  final List<HealthDevice> _discoveredDevices = [];
  bool _isSearching = false;
  Timer? _searchTimer;

  Stream<List<HealthDevice>> get devicesStream => _devicesController.stream;
  List<HealthDevice> get discoveredDevices => List.from(_discoveredDevices);
  bool get isSearching => _isSearching;

  // Start searching for health devices
  Future<bool> startDeviceSearch() async {
    if (_isSearching) return true;

    // Request necessary permissions
    final permissions = await _requestPermissions();
    if (!permissions) {
      debugPrint('Permissions denied for device search');
      return false;
    }

    _isSearching = true;
    _discoveredDevices.clear();
    
    // Simulate device discovery (in real app, this would use actual device APIs)
    _simulateDeviceDiscovery();
    
    return true;
  }

  // Stop device search
  Future<void> stopDeviceSearch() async {
    _isSearching = false;
    _searchTimer?.cancel();
    _searchTimer = null;
  }

  // Request necessary permissions
  Future<bool> _requestPermissions() async {
    final permissions = [
      Permission.location,
      Permission.locationWhenInUse,
    ];

    Map<Permission, PermissionStatus> statuses = await permissions.request();
    
    return statuses.values.every((status) => 
        status == PermissionStatus.granted || 
        status == PermissionStatus.limited);
  }

  // Simulate device discovery (replace with actual device search logic)
  void _simulateDeviceDiscovery() {
    final random = Random();
    int deviceCount = 0;

    _searchTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!_isSearching || deviceCount >= 10) {
        timer.cancel();
        _isSearching = false;
        return;
      }

      // Add a random health device
      final device = _generateRandomDevice(deviceCount);
      _discoveredDevices.add(device);
      deviceCount++;

      // Notify listeners
      _devicesController.add(List.from(_discoveredDevices));
    });
  }

  // Generate random health device for simulation
  HealthDevice _generateRandomDevice(int index) {
    final deviceTypes = [
      'Blood Pressure Monitor',
      'Glucose Meter',
      'Heart Rate Monitor',
      'Pulse Oximeter',
      'Digital Thermometer',
      'Smart Scale',
      'ECG Monitor',
      'Sleep Tracker',
      'Fitness Tracker',
      'Medication Dispenser',
    ];

    final manufacturers = [
      'HealthTech',
      'MediCorp',
      'VitalSigns',
      'CarePlus',
      'WellnessDevice',
      'SmartHealth',
      'MedConnect',
      'LifeMonitor',
    ];

    final random = Random();
    final deviceType = deviceTypes[index % deviceTypes.length];
    final manufacturer = manufacturers[random.nextInt(manufacturers.length)];
    
    return HealthDevice(
      id: 'device_${index + 1}',
      name: '$manufacturer $deviceType',
      type: deviceType,
      manufacturer: manufacturer,
      signalStrength: -30 - random.nextInt(70), // -30 to -100 dBm
      isConnected: false,
      batteryLevel: random.nextInt(100),
      lastSeen: DateTime.now(),
      capabilities: _getDeviceCapabilities(deviceType),
      modelNumber: 'Model-${random.nextInt(9999).toString().padLeft(4, '0')}',
      firmwareVersion: '${random.nextInt(5) + 1}.${random.nextInt(10)}.${random.nextInt(10)}',
    );
  }

  // Get capabilities based on device type
  List<String> _getDeviceCapabilities(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'blood pressure monitor':
        return ['Systolic BP', 'Diastolic BP', 'Heart Rate', 'Data Storage'];
      case 'glucose meter':
        return ['Blood Glucose', 'Data Logging', 'Trend Analysis'];
      case 'heart rate monitor':
        return ['Heart Rate', 'Heart Rate Variability', 'Activity Tracking'];
      case 'pulse oximeter':
        return ['SpO2', 'Pulse Rate', 'Perfusion Index'];
      case 'digital thermometer':
        return ['Body Temperature', 'Fever Alert', 'Memory Storage'];
      case 'smart scale':
        return ['Weight', 'BMI', 'Body Fat %', 'Muscle Mass'];
      case 'ecg monitor':
        return ['ECG Recording', 'Arrhythmia Detection', 'Heart Rate'];
      case 'sleep tracker':
        return ['Sleep Duration', 'Sleep Stages', 'Sleep Quality'];
      case 'fitness tracker':
        return ['Steps', 'Calories', 'Distance', 'Activity Minutes'];
      case 'medication dispenser':
        return ['Medication Reminders', 'Dose Tracking', 'Compliance Monitoring'];
      default:
        return ['Basic Monitoring', 'Data Storage'];
    }
  }

  // Connect to a device
  Future<bool> connectToDevice(String deviceId) async {
    final deviceIndex = _discoveredDevices.indexWhere((d) => d.id == deviceId);
    if (deviceIndex == -1) return false;

    // Simulate connection process
    await Future.delayed(const Duration(seconds: 2));

    _discoveredDevices[deviceIndex] = _discoveredDevices[deviceIndex].copyWith(
      isConnected: true,
      lastSeen: DateTime.now(),
    );

    _devicesController.add(List.from(_discoveredDevices));
    return true;
  }

  // Disconnect from a device
  Future<void> disconnectFromDevice(String deviceId) async {
    final deviceIndex = _discoveredDevices.indexWhere((d) => d.id == deviceId);
    if (deviceIndex == -1) return;

    _discoveredDevices[deviceIndex] = _discoveredDevices[deviceIndex].copyWith(
      isConnected: false,
    );

    _devicesController.add(List.from(_discoveredDevices));
  }

  // Get device data (simulated)
  Future<Map<String, dynamic>?> getDeviceData(String deviceId) async {
    final device = _discoveredDevices.firstWhere(
      (d) => d.id == deviceId,
      orElse: () => throw Exception('Device not found'),
    );

    if (!device.isConnected) return null;

    // Simulate data retrieval
    await Future.delayed(const Duration(seconds: 1));

    final random = Random();
    switch (device.type.toLowerCase()) {
      case 'blood pressure monitor':
        return {
          'systolic': 110 + random.nextInt(40),
          'diastolic': 70 + random.nextInt(20),
          'heartRate': 60 + random.nextInt(40),
          'timestamp': DateTime.now().toIso8601String(),
        };
      case 'glucose meter':
        return {
          'glucose': 80 + random.nextInt(120),
          'unit': 'mg/dL',
          'timestamp': DateTime.now().toIso8601String(),
        };
      case 'heart rate monitor':
        return {
          'heartRate': 60 + random.nextInt(40),
          'hrv': 20 + random.nextInt(60),
          'timestamp': DateTime.now().toIso8601String(),
        };
      case 'pulse oximeter':
        return {
          'spo2': 95 + random.nextInt(5),
          'pulseRate': 60 + random.nextInt(40),
          'timestamp': DateTime.now().toIso8601String(),
        };
      case 'digital thermometer':
        return {
          'temperature': 36.0 + random.nextDouble() * 3,
          'unit': '°C',
          'timestamp': DateTime.now().toIso8601String(),
        };
      case 'smart scale':
        return {
          'weight': 50 + random.nextInt(100),
          'bmi': 18.5 + random.nextDouble() * 15,
          'bodyFat': 10 + random.nextInt(30),
          'timestamp': DateTime.now().toIso8601String(),
        };
      default:
        return {
          'value': random.nextInt(100),
          'timestamp': DateTime.now().toIso8601String(),
        };
    }
  }

  // Clear discovered devices
  void clearDevices() {
    _discoveredDevices.clear();
    _devicesController.add([]);
  }

  // Dispose resources
  void dispose() {
    _searchTimer?.cancel();
    _devicesController.close();
  }
}

class HealthDevice {
  final String id;
  final String name;
  final String type;
  final String manufacturer;
  final int signalStrength; // in dBm
  final bool isConnected;
  final int batteryLevel; // percentage
  final DateTime lastSeen;
  final List<String> capabilities;
  final String modelNumber;
  final String firmwareVersion;

  const HealthDevice({
    required this.id,
    required this.name,
    required this.type,
    required this.manufacturer,
    required this.signalStrength,
    required this.isConnected,
    required this.batteryLevel,
    required this.lastSeen,
    required this.capabilities,
    required this.modelNumber,
    required this.firmwareVersion,
  });

  HealthDevice copyWith({
    String? id,
    String? name,
    String? type,
    String? manufacturer,
    int? signalStrength,
    bool? isConnected,
    int? batteryLevel,
    DateTime? lastSeen,
    List<String>? capabilities,
    String? modelNumber,
    String? firmwareVersion,
  }) {
    return HealthDevice(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      manufacturer: manufacturer ?? this.manufacturer,
      signalStrength: signalStrength ?? this.signalStrength,
      isConnected: isConnected ?? this.isConnected,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      lastSeen: lastSeen ?? this.lastSeen,
      capabilities: capabilities ?? this.capabilities,
      modelNumber: modelNumber ?? this.modelNumber,
      firmwareVersion: firmwareVersion ?? this.firmwareVersion,
    );
  }

  String get signalStrengthText {
    if (signalStrength >= -50) return 'Excellent';
    if (signalStrength >= -60) return 'Good';
    if (signalStrength >= -70) return 'Fair';
    return 'Poor';
  }

  IconData get deviceIcon {
    switch (type.toLowerCase()) {
      case 'blood pressure monitor':
        return Icons.favorite;
      case 'glucose meter':
        return Icons.water_drop;
      case 'heart rate monitor':
        return Icons.monitor_heart;
      case 'pulse oximeter':
        return Icons.sensors;
      case 'digital thermometer':
        return Icons.thermostat;
      case 'smart scale':
        return Icons.monitor_weight;
      case 'ecg monitor':
        return Icons.timeline;
      case 'sleep tracker':
        return Icons.bedtime;
      case 'fitness tracker':
        return Icons.fitness_center;
      case 'medication dispenser':
        return Icons.medical_services;
      default:
        return Icons.devices;
    }
  }
}
