name: doctor_appointment_app
description: A comprehensive Flutter app for managing doctor appointments with communication features.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI Components
  cupertino_icons: ^1.0.6
  flutter_launcher_icons: ^0.13.1
  
  # Database
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # HTTP and API
  http: ^1.1.0
  connectivity_plus: ^5.0.1
  
  # Communication
  url_launcher: ^6.2.1
  flutter_sms: ^2.3.3
  permission_handler: ^11.0.1
  
  # Notifications
  flutter_local_notifications: ^16.1.0
  timezone: ^0.9.2
  
  # Bluetooth
  flutter_bluetooth_serial: ^0.4.0
  
  # State Management and Navigation
  provider: ^6.1.1
  go_router: ^12.1.1
  
  # Date and Time
  intl: ^0.18.1
  
  # Shared Preferences
  shared_preferences: ^2.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
